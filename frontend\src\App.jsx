// frontend/src/App.jsx
import { Routes, Route, Link } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import RegisterPage from "./pages/RegisterPage";
import LoginPage from "./pages/LoginPage";
import DashboardPage from "./pages/DashboardPage";
import "./App.css";

// A simple component for a "Home" or landing page
const HomePage = () => (
  <div>
    <h1>Welcome to AcademiaSphere</h1>
    <p>Please log in or register.</p>
  </div>
);

function App() {
  return (
    <>
      {/* Toast notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />

      {/* Basic Navigation */}
      <nav>
        <Link to="/">Home</Link> | <Link to="/login">Login</Link> | <Link to="/register">Register</Link>
      </nav>

      <hr />

      {/* Route Definitions */}
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/dashboard" element={<DashboardPage />} />
      </Routes>
    </>
  );
}

export default App;




// // frontend/src/App.jsx
// import { useState, useEffect } from "react";
// import axios from "axios"; // Import axios
// import reactLogo from "./assets/react.svg";
// import "./App.css";

// function App() {
//   // Create a state variable to hold our message from the API
//   const [message, setMessage] = useState("");
//   // Create a state variable to handle loading state
//   const [loading, setLoading] = useState(true);

//   // useEffect is a React hook that runs code after the component renders.
//   // The empty array [] at the end means it will only run ONCE.
//   useEffect(() => {
//     // Define the API endpoint URL.
//     const apiUrl = "http://localhost:8000/api/v1/hello/";

//     // Use axios to make a GET request to our backend.
//     axios
//       .get(apiUrl)
//       .then((response) => {
//         // If the request is successful, update our state with the message
//         setMessage(response.data.message);
//       })
//       .catch((error) => {
//         // If there's an error, log it and set an error message
//         console.error("Error fetching data:", error);
//         setMessage("Could not connect to the API.");
//       })
//       .finally(() => {
//         // This runs whether the request was successful or not
//         setLoading(false);
//       });
//   }, []); // The empty dependency array ensures this runs only once on mount

//   return (
//     <>
//       <div>
//         <a href="https://react.dev" target="_blank" rel="noreferrer">
//           <img src={reactLogo} className="logo react" alt="React logo" />
//         </a>
//       </div>
//       <h1>AcademiaSphere + React</h1>
//       <div className="card">
//         <h2>API Connection Test</h2>
//         {/* Conditionally render content based on the loading state */}
//         {loading ? (
//           <p>Loading message from backend...</p>
//         ) : (
//           <p>
//             <code>{message}</code>
//           </p>
//         )}
//       </div>
//     </>
//   );
// }

// export default App;

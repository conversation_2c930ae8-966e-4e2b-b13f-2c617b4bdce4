import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import "./RegisterPage.css";

const RegisterPage = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    first_name: "",
    last_name: "",
    password: "",
    password_confirm: "",
    invitation_code: "",
  });
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (errors[e.target.name]) {
      setErrors({
        ...errors,
        [e.target.name]: "",
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) newErrors.email = "Email is required";
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = "Email is invalid";

    if (!formData.first_name) newErrors.first_name = "First name is required";
    if (!formData.last_name) newErrors.last_name = "Last name is required";

    if (!formData.password) newErrors.password = "Password is required";
    else if (formData.password.length < 8) newErrors.password = "Password must be at least 8 characters";

    if (!formData.password_confirm) newErrors.password_confirm = "Please confirm your password";
    else if (formData.password !== formData.password_confirm) newErrors.password_confirm = "Passwords do not match";

    if (!formData.invitation_code) newErrors.invitation_code = "Invitation code is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    try {
      console.log("Registration data:", formData);

      setTimeout(() => {
        alert("Account created successfully!");
        navigate("/login");
      }, 1000);
    } catch (error) {
      console.error("Registration error:", error);
      alert("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="register-container">
      <div className="register-card">
        <div className="register-header">
          <h1 className="register-title">AcademiaSphere</h1>
          <p className="register-subtitle">Create your research account</p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">First Name *</label>
            <input
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={handleChange}
              className={`form-input ${errors.first_name ? "error" : ""}`}
              placeholder="Enter your first name"
            />
            {errors.first_name && <div className="error-message">{errors.first_name}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Last Name *</label>
            <input
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={handleChange}
              className={`form-input ${errors.last_name ? "error" : ""}`}
              placeholder="Enter your last name"
            />
            {errors.last_name && <div className="error-message">{errors.last_name}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Email Address *</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className={`form-input ${errors.email ? "error" : ""}`}
              placeholder="<EMAIL>"
            />
            {errors.email && <div className="error-message">{errors.email}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Password *</label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className={`form-input ${errors.password ? "error" : ""}`}
              placeholder="Create a strong password"
            />
            {errors.password && <div className="error-message">{errors.password}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Confirm Password *</label>
            <input
              type="password"
              name="password_confirm"
              value={formData.password_confirm}
              onChange={handleChange}
              className={`form-input ${errors.password_confirm ? "error" : ""}`}
              placeholder="Confirm your password"
            />
            {errors.password_confirm && <div className="error-message">{errors.password_confirm}</div>}
          </div>

          <div className="form-group">
            <label className="form-label">Invitation Code *</label>
            <input
              type="text"
              name="invitation_code"
              value={formData.invitation_code}
              onChange={handleChange}
              className={`form-input ${errors.invitation_code ? "error" : ""}`}
              placeholder="Enter invitation code"
            />
            {errors.invitation_code && <div className="error-message">{errors.invitation_code}</div>}
          </div>

          <button type="submit" disabled={isLoading} className="submit-button">
            {isLoading ? (
              <>
                <span className="loading-spinner"></span>
                Creating Account...
              </>
            ) : (
              "Create Account"
            )}
          </button>
        </form>

        <div style={{ textAlign: "center", marginTop: "1rem" }}>
          <p style={{ color: "#6b7280", fontSize: "0.875rem" }}>
            Already have an account?{" "}
            <Link to="/login" className="link">
              Sign in here
            </Link>
          </p>
        </div>

        <div className="info-box">💡 Contact your research group leader for an invitation code.</div>
      </div>
    </div>
  );
};

export default RegisterPage;



// import React, { useState } from "react";
// import { Link, useNavigate } from "react-router-dom";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { z } from "zod";
// import { toast } from "react-hot-toast";

// // Services
// import { registerUser } from "../services/authService";

// const registerSchema = z
//   .object({
//     email: z.string().email("Please enter a valid email address."),
//     first_name: z.string().min(1, "First name is required.").max(150, "First name is too long."),
//     last_name: z.string().min(1, "Last name is required.").max(150, "Last name is too long."),
//     password: z
//       .string()
//       .min(8, "Password must be at least 8 characters long.")
//       .regex(/[A-Z]/, "Password must contain at least one uppercase letter.")
//       .regex(/[a-z]/, "Password must contain at least one lowercase letter.")
//       .regex(/[0-9]/, "Password must contain at least one number."),
//     password_confirm: z.string(),
//     invitation_code: z.string().uuid("Please enter a valid invitation code.").min(1, "Invitation code is required."),
//   })
//   .refine((data) => data.password === data.password_confirm, {
//     message: "Passwords do not match.",
//     path: ["password_confirm"],
//   });

// const RegisterPage = () => {
//   const navigate = useNavigate();
//   const [isLoading, setIsLoading] = useState(false);
//   const [showPassword, setShowPassword] = useState(false);

//   const {
//     register,
//     handleSubmit,
//     formState: { errors },
//     setError,
//     watch,
//   } = useForm({
//     resolver: zodResolver(registerSchema),
//     defaultValues: {
//       email: "",
//       first_name: "",
//       last_name: "",
//       password: "",
//       password_confirm: "",
//       invitation_code: "",
//     },
//   });

//   const password = watch("password");

//   const onSubmit = async (data) => {
//     setIsLoading(true);
//     try {
//       const response = await registerUser(data);
//       toast.success(response.message || "Account created successfully! Welcome to AcademiaSphere.");

//       // Store tokens if provided
//       if (response.access_token) {
//         localStorage.setItem("access_token", response.access_token);
//         localStorage.setItem("refresh_token", response.refresh_token);
//       }

//       navigate("/dashboard", { replace: true });
//     } catch (error) {
//       console.error("Registration error:", error);

//       // Handle specific backend validation errors
//       if (error.response?.data) {
//         const backendErrors = error.response.data;

//         // Set field-specific errors from the backend
//         Object.keys(backendErrors).forEach((field) => {
//           setError(field, {
//             type: "server",
//             message: Array.isArray(backendErrors[field]) ? backendErrors[field].join(", ") : backendErrors[field],
//           });
//         });

//         // If it's a general error, show a toast
//         if (backendErrors.detail) {
//           toast.error(backendErrors.detail);
//         }
//       } else {
//         toast.error(error.message || "An unexpected error occurred. Please try again.");
//       }
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const getPasswordStrength = (password) => {
//     if (!password) return { strength: 0, color: "bg-gray-300", text: "" };

//     let strength = 0;
//     if (password.length >= 8) strength++;
//     if (/[A-Z]/.test(password)) strength++;
//     if (/[a-z]/.test(password)) strength++;
//     if (/[0-9]/.test(password)) strength++;
//     if (/[^A-Za-z0-9]/.test(password)) strength++;

//     const colors = ["bg-red-500", "bg-orange-500", "bg-yellow-500", "bg-blue-500", "bg-green-500"];

//     const texts = ["Very Weak", "Weak", "Medium", "Strong", "Very Strong"];

//     return {
//       strength: (strength / 5) * 100,
//       color: colors[strength - 1] || "bg-gray-300",
//       text: texts[strength - 1] || "",
//     };
//   };

//   const passwordStrength = getPasswordStrength(password);

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-academia-dark via-academia-purple to-academia-dark flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-4xl w-full bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden mx-4">
//         <div className="md:flex">
//           {/* Left Side - Branding (Hidden on mobile) */}
//           <div className="md:w-2/5 bg-gradient-to-br from-academia-blue to-academia-purple p-8 text-white hidden md:block">
//             <div className="flex flex-col justify-center h-full">
//               <div className="text-center">
//                 <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-4">
//                   <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
//                     <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
//                   </svg>
//                 </div>
//                 <h1 className="text-2xl font-bold mb-3">AcademiaSphere</h1>
//                 <p className="text-blue-100 text-sm mb-6">Join the premier platform for academic collaboration</p>
//                 <div className="space-y-2 text-left text-sm">
//                   <div className="flex items-center">
//                     <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
//                       <path
//                         fillRule="evenodd"
//                         d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
//                         clipRule="evenodd"
//                       />
//                     </svg>
//                     <span>Real-time collaboration</span>
//                   </div>
//                   <div className="flex items-center">
//                     <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
//                       <path
//                         fillRule="evenodd"
//                         d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
//                         clipRule="evenodd"
//                       />
//                     </svg>
//                     <span>Secure research workspaces</span>
//                   </div>
//                   <div className="flex items-center">
//                     <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
//                       <path
//                         fillRule="evenodd"
//                         d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
//                         clipRule="evenodd"
//                       />
//                     </svg>
//                     <span>Seamless supervision tools</span>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>

//           {/* Right Side - Registration Form */}
//           <div className="md:w-3/5 p-6 sm:p-8">
//             {/* Mobile Header */}
//             <div className="md:hidden text-center mb-6">
//               <div className="w-12 h-12 bg-gradient-to-r from-academia-blue to-academia-purple rounded-lg flex items-center justify-center mx-auto mb-3">
//                 <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
//                   <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
//                 </svg>
//               </div>
//               <h1 className="text-2xl font-bold text-gray-800">AcademiaSphere</h1>
//               <p className="text-gray-600 text-sm">Create your account</p>
//             </div>

//             <div className="text-center mb-6">
//               <h2 className="text-2xl font-bold text-gray-800 mb-1">Create Your Account</h2>
//               <p className="text-gray-600 text-sm">Join your research community today</p>
//             </div>

//             <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
//               <div className="grid md:grid-cols-2 gap-3">
//                 <div>
//                   <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 mb-1">
//                     First Name *
//                   </label>
//                   <input
//                     id="first_name"
//                     type="text"
//                     {...register("first_name")}
//                     className={`w-full px-3 py-2 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-2 ${
//                       errors.first_name
//                         ? "border-red-500 bg-red-50 focus:ring-red-500"
//                         : "border-gray-300 bg-gray-50 focus:ring-academia-blue"
//                     }`}
//                     placeholder="First name"
//                   />
//                   {errors.first_name && <p className="mt-1 text-xs text-red-600">{errors.first_name.message}</p>}
//                 </div>

//                 <div>
//                   <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 mb-1">
//                     Last Name *
//                   </label>
//                   <input
//                     id="last_name"
//                     type="text"
//                     {...register("last_name")}
//                     className={`w-full px-3 py-2 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-2 ${
//                       errors.last_name
//                         ? "border-red-500 bg-red-50 focus:ring-red-500"
//                         : "border-gray-300 bg-gray-50 focus:ring-academia-blue"
//                     }`}
//                     placeholder="Last name"
//                   />
//                   {errors.last_name && <p className="mt-1 text-xs text-red-600">{errors.last_name.message}</p>}
//                 </div>
//               </div>

//               <div>
//                 <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
//                   Email Address *
//                 </label>
//                 <input
//                   id="email"
//                   type="email"
//                   {...register("email")}
//                   className={`w-full px-3 py-2 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-2 ${
//                     errors.email
//                       ? "border-red-500 bg-red-50 focus:ring-red-500"
//                       : "border-gray-300 bg-gray-50 focus:ring-academia-blue"
//                   }`}
//                   placeholder="<EMAIL>"
//                 />
//                 {errors.email && <p className="mt-1 text-xs text-red-600">{errors.email.message}</p>}
//               </div>

//               <div>
//                 <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
//                   Password *
//                 </label>
//                 <div className="relative">
//                   <input
//                     id="password"
//                     type={showPassword ? "text" : "password"}
//                     {...register("password")}
//                     className={`w-full px-3 py-2 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-2 pr-10 ${
//                       errors.password
//                         ? "border-red-500 bg-red-50 focus:ring-red-500"
//                         : "border-gray-300 bg-gray-50 focus:ring-academia-blue"
//                     }`}
//                     placeholder="Create a strong password"
//                   />
//                   <button
//                     type="button"
//                     onClick={() => setShowPassword(!showPassword)}
//                     className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
//                   >
//                     <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                       {showPassword ? (
//                         <>
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth={2}
//                             d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
//                           />
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth={2}
//                             d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
//                           />
//                         </>
//                       ) : (
//                         <>
//                           <path
//                             strokeLinecap="round"
//                             strokeLinejoin="round"
//                             strokeWidth={2}
//                             d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
//                           />
//                         </>
//                       )}
//                     </svg>
//                   </button>
//                 </div>
//                 {errors.password && <p className="mt-1 text-xs text-red-600">{errors.password.message}</p>}

//                 {/* Password Strength Meter */}
//                 {password && (
//                   <div className="mt-2">
//                     <div className="flex justify-between items-center mb-1">
//                       <span className="text-xs text-gray-600">Strength:</span>
//                       <span className={`text-xs font-medium ${passwordStrength.color.replace("bg-", "text-")}`}>
//                         {passwordStrength.text}
//                       </span>
//                     </div>
//                     <div className="w-full bg-gray-200 rounded-full h-1.5">
//                       <div
//                         className={`h-1.5 rounded-full transition-all duration-300 ${passwordStrength.color}`}
//                         style={{ width: `${passwordStrength.strength}%` }}
//                       ></div>
//                     </div>
//                   </div>
//                 )}
//               </div>

//               <div>
//                 <label htmlFor="password_confirm" className="block text-sm font-medium text-gray-700 mb-1">
//                   Confirm Password *
//                 </label>
//                 <input
//                   id="password_confirm"
//                   type={showPassword ? "text" : "password"}
//                   {...register("password_confirm")}
//                   className={`w-full px-3 py-2 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-2 ${
//                     errors.password_confirm
//                       ? "border-red-500 bg-red-50 focus:ring-red-500"
//                       : "border-gray-300 bg-gray-50 focus:ring-academia-blue"
//                   }`}
//                   placeholder="Confirm your password"
//                 />
//                 {errors.password_confirm && (
//                   <p className="mt-1 text-xs text-red-600">{errors.password_confirm.message}</p>
//                 )}
//               </div>

//               <div>
//                 <label htmlFor="invitation_code" className="block text-sm font-medium text-gray-700 mb-1">
//                   Invitation Code *
//                 </label>
//                 <input
//                   id="invitation_code"
//                   type="text"
//                   {...register("invitation_code")}
//                   className={`w-full px-3 py-2 border rounded-lg transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-2 ${
//                     errors.invitation_code
//                       ? "border-red-500 bg-red-50 focus:ring-red-500"
//                       : "border-gray-300 bg-gray-50 focus:ring-academia-blue"
//                   }`}
//                   placeholder="Enter invitation code"
//                 />
//                 {errors.invitation_code && (
//                   <p className="mt-1 text-xs text-red-600">{errors.invitation_code.message}</p>
//                 )}
//                 <p className="mt-1 text-xs text-gray-600">Contact your research group leader for an invitation code.</p>
//               </div>

//               <button
//                 type="submit"
//                 disabled={isLoading}
//                 className="w-full bg-gradient-to-r from-academia-blue to-academia-purple hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-2.5 px-4 rounded-lg transition-all duration-200 hover:scale-[1.02] disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center space-x-2 shadow-md hover:shadow-lg text-sm"
//               >
//                 {isLoading ? (
//                   <>
//                     <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
//                     <span>Creating Account...</span>
//                   </>
//                 ) : (
//                   <>
//                     <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                       <path
//                         strokeLinecap="round"
//                         strokeLinejoin="round"
//                         strokeWidth={2}
//                         d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
//                       />
//                     </svg>
//                     <span>Create Account</span>
//                   </>
//                 )}
//               </button>
//             </form>

//             <div className="mt-6 text-center">
//               <p className="text-sm text-gray-600">
//                 Already have an account?{" "}
//                 <Link
//                   to="/login"
//                   className="font-semibold text-academia-blue hover:text-blue-500 transition-colors duration-200"
//                 >
//                   Sign in here
//                 </Link>
//               </p>
//             </div>

//             <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
//               <div className="flex items-start space-x-2">
//                 <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
//                   <path
//                     fillRule="evenodd"
//                     d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
//                     clipRule="evenodd"
//                   />
//                 </svg>
//                 <div>
//                   <p className="text-xs text-blue-800">
//                     Your invitation code ensures you join the correct research group and institution. Required for
//                     academic verification.
//                   </p>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default RegisterPage;

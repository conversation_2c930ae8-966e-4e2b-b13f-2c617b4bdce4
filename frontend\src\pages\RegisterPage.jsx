// frontend/src/pages/RegisterPage.jsx (with enhanced Tailwind)
import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "react-hot-toast";

// Components
import LoadingSpinner from "../components/ui/LoadingSpinner";
import FormInput from "../components/forms/FormInput";

// Services
import { registerUser } from "../services/authService";

// Validation Schema
const registerSchema = z
  .object({
    email: z.string().email("Please enter a valid email address."),
    first_name: z.string().min(1, "First name is required.").max(150, "First name is too long."),
    last_name: z.string().min(1, "Last name is required.").max(150, "Last name is too long."),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters long.")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter.")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter.")
      .regex(/[0-9]/, "Password must contain at least one number."),
    password_confirm: z.string(),
    invitation_code: z.string().uuid("Please enter a valid invitation code.").min(1, "Invitation code is required."),
  })
  .refine((data) => data.password === data.password_confirm, {
    message: "Passwords do not match.",
    path: ["password_confirm"],
  });

const RegisterPage = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: "",
      first_name: "",
      last_name: "",
      password: "",
      password_confirm: "",
      invitation_code: "",
    },
  });

  const onSubmit = async (data) => {
    setIsLoading(true);
    try {
      const response = await registerUser(data);
      toast.success(response.message || "Registration successful!");

      if (response.access_token) {
        localStorage.setItem("access_token", response.access_token);
        localStorage.setItem("refresh_token", response.refresh_token);
      }

      navigate("/dashboard", { replace: true });
    } catch (error) {
      console.error("Registration error:", error);
      if (error.response?.data) {
        const backendErrors = error.response.data;
        Object.keys(backendErrors).forEach((field) => {
          setError(field, {
            type: "server",
            message: Array.isArray(backendErrors[field]) ? backendErrors[field].join(", ") : backendErrors[field],
          });
        });
        if (backendErrors.detail) {
          toast.error(backendErrors.detail);
        }
      } else {
        toast.error(error.message || "An unexpected error occurred. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl overflow-hidden">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-6 px-6 text-center">
          <h1 className="text-3xl font-bold text-white mb-2">AcademiaSphere</h1>
          <p className="text-blue-100 text-sm">Join your research community</p>
        </div>

        {/* Form Section */}
        <div className="px-8 py-6">
          <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">Create Your Account</h2>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <FormInput
              id="email"
              type="email"
              autoComplete="email"
              placeholder="Email address"
              error={errors.email}
              {...register("email")}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormInput
                id="first_name"
                type="text"
                autoComplete="given-name"
                placeholder="First name"
                error={errors.first_name}
                {...register("first_name")}
              />

              <FormInput
                id="last_name"
                type="text"
                autoComplete="family-name"
                placeholder="Last name"
                error={errors.last_name}
                {...register("last_name")}
              />
            </div>

            <FormInput
              id="password"
              type="password"
              autoComplete="new-password"
              placeholder="Password"
              error={errors.password}
              {...register("password")}
            />

            <FormInput
              id="password_confirm"
              type="password"
              autoComplete="new-password"
              placeholder="Confirm password"
              error={errors.password_confirm}
              {...register("password_confirm")}
            />

            <FormInput
              id="invitation_code"
              type="text"
              autoComplete="off"
              placeholder="Invitation code"
              error={errors.invitation_code}
              {...register("invitation_code")}
            />

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner className="h-5 w-5" />
                  <span>Creating Account...</span>
                </>
              ) : (
                <span>Create Account</span>
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link
                to="/login"
                className="font-semibold text-blue-600 hover:text-blue-500 transition-colors duration-200"
              >
                Sign in here
              </Link>
            </p>
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-xs text-blue-700 text-center">
              💡 Don't have an invitation code? Contact your research group leader or institution administrator.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;




// import React, { useState } from "react";
// import { Link, useNavigate } from "react-router-dom";
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { z } from "zod";
// import { toast } from "react-hot-toast";

// // Components
// import LoadingSpinner from "../components/ui/LoadingSpinner";
// import FormInput from "../components/forms/FormInput";

// // Services
// import { registerUser } from "../services/authService";

// // Validation Schema using Zod
// const registerSchema = z
//   .object({
//     email: z.string().email("Please enter a valid email address."),
//     first_name: z.string().min(1, "First name is required.").max(150, "First name is too long."),
//     last_name: z.string().min(1, "Last name is required.").max(150, "Last name is too long."),
//     password: z
//       .string()
//       .min(8, "Password must be at least 8 characters long.")
//       .regex(/[A-Z]/, "Password must contain at least one uppercase letter.")
//       .regex(/[a-z]/, "Password must contain at least one lowercase letter.")
//       .regex(/[0-9]/, "Password must contain at least one number."),
//     password_confirm: z.string(),
//     invitation_code: z.string().uuid("Please enter a valid invitation code.").min(1, "Invitation code is required."),
//   })
//   .refine((data) => data.password === data.password_confirm, {
//     message: "Passwords do not match.",
//     path: ["password_confirm"], // Path of the error
//   });

// const RegisterPage = () => {
//   const navigate = useNavigate();
//   const [isLoading, setIsLoading] = useState(false);

//   const {
//     register,
//     handleSubmit,
//     formState: { errors },
//     setError,
//   } = useForm({
//     resolver: zodResolver(registerSchema),
//     defaultValues: {
//       email: "",
//       first_name: "",
//       last_name: "",
//       password: "",
//       password_confirm: "",
//       invitation_code: "",
//     },
//   });

//   const onSubmit = async (data) => {
//     setIsLoading(true);
//     try {
//       // Use the centralized API service
//       const response = await registerUser(data);

//       // Success: Show toast and redirect
//       toast.success(response.message || "Registration successful!");

//       // Store tokens (consider using a context/state management for this)
//       if (response.access_token) {
//         localStorage.setItem("access_token", response.access_token);
//         localStorage.setItem("refresh_token", response.refresh_token);
//       }

//       // Redirect to dashboard or login page
//       navigate("/dashboard", { replace: true });
//     } catch (error) {
//       console.error("Registration error:", error);

//       // Handle specific backend validation errors
//       if (error.response?.data) {
//         const backendErrors = error.response.data;

//         // Set field-specific errors from the backend
//         Object.keys(backendErrors).forEach((field) => {
//           // Map backend field names to form field names if necessary
//           const formField = field; // e.g., 'invitation_code' -> 'invitation_code'
//           setError(formField, {
//             type: "server",
//             message: Array.isArray(backendErrors[field]) ? backendErrors[field].join(", ") : backendErrors[field],
//           });
//         });

//         // If it's a general error, show a toast
//         if (backendErrors.detail) {
//           toast.error(backendErrors.detail);
//         }
//       } else {
//         // Generic error
//         toast.error(error.message || "An unexpected error occurred. Please try again.");
//       }
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-md w-full space-y-8">
//         {/* Header */}
//         <div>
//           <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">Create your account</h2>
//           <p className="mt-2 text-center text-sm text-gray-600">
//             Or{" "}
//             <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500">
//               sign in to your existing account
//             </Link>
//           </p>
//         </div>

//         {/* Form */}
//         <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)} noValidate>
//           <div className="rounded-md shadow-sm -space-y-px">
//             <FormInput
//               id="email"
//               type="email"
//               autoComplete="email"
//               placeholder="Email address"
//               error={errors.email}
//               {...register("email")}
//             />

//             <FormInput
//               id="first_name"
//               type="text"
//               autoComplete="given-name"
//               placeholder="First name"
//               error={errors.first_name}
//               {...register("first_name")}
//             />

//             <FormInput
//               id="last_name"
//               type="text"
//               autoComplete="family-name"
//               placeholder="Last name"
//               error={errors.last_name}
//               {...register("last_name")}
//             />

//             <FormInput
//               id="password"
//               type="password"
//               autoComplete="new-password"
//               placeholder="Password"
//               error={errors.password}
//               {...register("password")}
//             />

//             <FormInput
//               id="password_confirm"
//               type="password"
//               autoComplete="new-password"
//               placeholder="Confirm password"
//               error={errors.password_confirm}
//               {...register("password_confirm")}
//             />

//             <FormInput
//               id="invitation_code"
//               type="text"
//               autoComplete="off"
//               placeholder="Invitation code"
//               error={errors.invitation_code}
//               {...register("invitation_code")}
//             />
//           </div>

//           {/* Submit Button */}
//           <div>
//             <button
//               type="submit"
//               disabled={isLoading}
//               className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
//             >
//               {isLoading ? <LoadingSpinner className="h-5 w-5" /> : "Create account"}
//             </button>
//           </div>

//           {/* Help Text */}
//           <div className="text-center">
//             <p className="text-sm text-gray-600">
//               Don't have an invitation code? Contact your research group leader or institution administrator.
//             </p>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default RegisterPage;

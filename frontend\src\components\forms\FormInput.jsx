import React from "react";

const FormInput = React.forwardRef(({ id, type, placeholder, error, ...props }, ref) => {
  return (
    <div className="space-y-1">
      <label htmlFor={id} className="sr-only">
        {placeholder}
      </label>
      <input
        id={id}
        ref={ref}
        type={type}
        className={`w-full px-4 py-3 border-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
          error ? "border-red-500 bg-red-50 focus:ring-red-500" : "border-gray-300 bg-gray-50 focus:ring-blue-500"
        } placeholder-gray-500 text-gray-900`}
        placeholder={placeholder}
        {...props}
      />
      {error && <p className="text-sm text-red-600 font-medium">{error.message}</p>}
    </div>
  );
});

FormInput.displayName = "FormInput";

export default FormInput;




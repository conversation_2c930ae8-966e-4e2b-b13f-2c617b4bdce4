from rest_framework import permissions
from django.core.exceptions import ObjectDoesNotExist

class IsResearchGroupLeader(permissions.BasePermission):
    """
    Allows access only to users who are leaders of a research group.
    This is a general permission for views that require group leadership (e.g., to create an invitation).
    """
    def has_permission(self, request, view):
        # Check if the user is authenticated and leads at least one group
        return bool(request.user and request.user.is_authenticated and request.user.led_groups.exists())


class IsLeaderOfGroup(permissions.BasePermission):
    """
    Object-level permission to only allow the leader of a *specific* research group to access it.
    Assumes the view has a `research_group` object or the object itself is a ResearchGroup.
    """
    def has_object_permission(self, request, view, obj):
        # Handle cases where the obj is the ResearchGroup itself
        if hasattr(obj, 'research_group'):
            # obj is a related object (like a Workspace), check its group
            return obj.research_group.leader == request.user
        elif hasattr(obj, 'led_groups'):
            # obj is a User profile, check if the requesting user is their leader? (Unlikely use case)
            # This logic depends heavily on the use case.
            return False
        else:
            # obj is the ResearchGroup
            return obj.leader == request.user


class IsMemberOfGroup(permissions.BasePermission):
    """
    Object-level permission to allow any member (leader or regular member) of a specific research group to access it.
    """
    def has_object_permission(self, request, view, obj):
        # Get the research group from the object
        research_group = None
        if hasattr(obj, 'research_group'):
            research_group = obj.research_group
        elif isinstance(obj, ResearchGroup):
            research_group = obj
        else:
            # If the object isn't related to a group, default to False for safety.
            return False

        # Check if the user is the leader OR a member of the group
        return research_group.leader == request.user or research_group.members.filter(id=request.user.id).exists()


class IsInstitutionAdmin(permissions.BasePermission):
    """
    Permission to only allow administrators of the user's institution.
    Can be used for global admin actions within their institution.
    """
    def has_permission(self, request, view):
        user = request.user
        if not user or not user.is_authenticated or not user.institution:
            return False
        # Check if the user is an admin for their institution
        return user.institution.administrators.filter(user=user).exists()

    def has_object_permission(self, request, view, obj):
        # For object-level checks, ensure the object belongs to the user's institution.
        # This requires the object to have an `institution` field or a way to resolve it.
        user_institution = request.user.institution

        if hasattr(obj, 'institution'):
            # The object has a direct FK to Institution (e.g., ResearchGroup)
            return obj.institution == user_institution
        elif hasattr(obj, 'get_institution'):
            # The object has a method to get its institution (e.g., a User)
            return obj.get_institution() == user_institution
        elif hasattr(obj, 'research_group') and hasattr(obj.research_group, 'institution'):
            # The object belongs to a group, which belongs to an institution (e.g., a Workspace)
            return obj.research_group.institution == user_institution
        else:
            # If we can't determine the institution, be safe and deny access.
            return False


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Object-level permission that allows full access to the owner,
    but allows read-only access to other authenticated users with group membership.
    Useful for resources like files or wiki pages.
    """
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any group member for safe methods (GET, HEAD, OPTIONS)
        if request.method in permissions.SAFE_METHODS:
            # Re-use the logic from IsMemberOfGroup
            member_perm = IsMemberOfGroup()
            return member_perm.has_object_permission(request, view, obj)

        # Write permissions are only allowed to the owner of the object.
        return obj.owner == request.user


# --- Utility Permission for ViewSets ---
class GetIfMemberPostIfLeader(permissions.BasePermission):
    """
    Permission class for ViewSets that handles common CRUD patterns:
    - SAFE_METHODS (GET, etc.): Require group membership.
    - "DESTRUCTIVE" METHODS (POST, PUT, PATCH, DELETE): Require group leadership.
    This is useful for a viewset where you list groups (GET) or create them (POST).
    """
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            # Any authenticated user can try to list/retrieve.
            # Further object-level filtering will happen in the queryset.
            return request.user and request.user.is_authenticated
        else:
            # Only group leaders can create new groups.
            leader_perm = IsResearchGroupLeader()
            return leader_perm.has_permission(request, view)







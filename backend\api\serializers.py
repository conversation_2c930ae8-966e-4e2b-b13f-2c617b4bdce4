from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from .models import User, Institution

class InstitutionSerializer(serializers.ModelSerializer):
    """
    Serializer for the Institution model.
    Used for display and superuser creation.
    """
    class Meta:
        model = Institution
        fields = ('id', 'name', 'parent_institution', 'is_active', 'created_at')
        read_only_fields = ('id', 'created_at')

class UserSerializer(serializers.ModelSerializer):
    """
    General serializer for User details (safe for users to see their own info).
    """
    institution_details = InstitutionSerializer(source='institution', read_only=True)
    full_name = serializers.CharField(source='get_full_name', read_only=True)

    class Meta:
        model = User
        fields = (
            'id', 'email', 'first_name', 'last_name', 'full_name',
            'institution', 'institution_details', 'role',
            'is_active', 'date_joined'
        )
        read_only_fields = ('id', 'date_joined', 'is_active', 'institution_details')
        # For most users, 'institution' and 'role' should be read-only and managed by admins.
        # We'll control this in the view.

class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for user registration.
    Requires an existing institution and an invitation code (handled by the view).
    """
    password = serializers.CharField(
        write_only=True,
        required=True,
        validators=[validate_password], # Enforce strong passwords
        style={'input_type': 'password'}
    )
    password_confirm = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )
    # User provides a code, NOT an institution name.
    # The view will resolve this code to an institution.
    invitation_code = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        fields = ('email', 'password', 'password_confirm', 'first_name', 'last_name', 'invitation_code')
        # All fields are required for registration
        extra_kwargs = {
            'first_name': {'required': True},
            'last_name': {'required': True},
        }

    def validate(self, attrs):
        """
        Object-level validation (checks across multiple fields).
        """
        # 1. Check passwords match
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({"password_confirm": "Password fields did not match."})

        # 2. The invitation_code is validated in the view, not here, because
        # it requires database lookups that are context-dependent (e.g., is the code expired?).
        # The view will use a service function to validate the code and add the 'institution' to attrs.

        return attrs

    def create(self, validated_data):
        """
        Creates a new user. Expects 'institution' to be injected into validated_data by the view.
        """
        # Remove fields that are not part of the User model
        validated_data.pop('password_confirm')
        invitation_code = validated_data.pop('invitation_code') # This has been validated by the view

        # The 'institution' must be provided by the view after validating the code.
        # If it's not there, it's a programming error.
        institution = validated_data.get('institution')
        if not institution:
            raise serializers.ValidationError(
                {"invitation_code": "Could not validate invitation. Please request a new one."}
            )

        # Create the user with the assigned institution
        user = User.objects.create_user(
            **validated_data,
            institution=institution,
            # Default role for invited users is STUDENT.
            # This could be made configurable via the invitation.
            role=User.UserRole.STUDENT
        )
        return user

class InvitationSerializer(serializers.Serializer):
    """
    Serializer for generating invitations. Not a ModelSerializer.
    """
    # This would be used by a Group Leader to create a new invite code
    email = serializers.EmailField(required=False) # Optional: pre-fill the invite for a specific user
    role = serializers.ChoiceField(choices=User.UserRole.choices, default=User.UserRole.STUDENT)
    expires_in = serializers.IntegerField(default=7, min_value=1, max_value=30) # Days until expiry

class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer for password change endpoint.
    """
    old_password = serializers.CharField(required=True, write_only=True)
    new_password = serializers.CharField(
        required=True,
        write_only=True,
        validators=[validate_password],
        style={'input_type': 'password'}
    )
    new_password_confirm = serializers.CharField(required=True, write_only=True, style={'input_type': 'password'})

    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("Your current password was entered incorrectly.")
        return value

    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError({"new_password_confirm": "The new password fields did not match."})
        if attrs['old_password'] == attrs['new_password']:
            raise serializers.ValidationError({"new_password": "New password cannot be the same as the old password."})
        return attrs

    def save(self, **kwargs):
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user








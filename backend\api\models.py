import uuid
from datetime import <PERSON><PERSON><PERSON>
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.db.models import F, Q # Import F and Q for constraints

# =============================================================================
# 1. FOUNDATIONAL MODELS (Institution and User Identity)
#    These are the core pillars of the multi-tenant system.
# =============================================================================


class InstitutionManager(models.Manager):
    """Custom manager for Institution to handle the hierarchy."""
    def get_queryset(self):
        return super().get_queryset().select_related('parent_institution')


class Institution(models.Model):
    """
    The top-level Tenant model. Represents a University or Organization.
    Can have a hierarchical structure: University -> Faculty -> School/Department.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    # Self-referencing FK for hierarchy. A University's parent is NULL.
    parent_institution = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        related_name='child_institutions',
        null=True,
        blank=True
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    objects = InstitutionManager()

    class Meta:
        # A unique name is only enforced within the same hierarchy level.
        unique_together = ['name', 'parent_institution']
        constraints = [
            # Prevent an institution from being its own parent (directly or indirectly would require more complex checks)
            models.CheckConstraint(
                check=~models.Q(id=models.F('parent_institution')),
                name='check_institution_not_self_parent'
            ),
        ]

    def clean(self):
        # Prevent circular references in the hierarchy (basic check)
        if self.parent_institution and self.parent_institution == self:
            raise ValidationError({'parent_institution': _('An institution cannot be its own parent.')})
        # You could add more complex logic here to prevent deep circular refs

    def __str__(self):
        return f"{self.parent_institution.name + ' -> ' if self.parent_institution else ''}{self.name}"

    @property
    def is_root(self):
        """Check if this is a root institution (e.g., a University)."""
        return self.parent_institution is None

    def get_root(self):
        """Get the root institution for this node."""
        institution = self
        while institution.parent_institution is not None:
            institution = institution.parent_institution
        return institution


class User(AbstractBaseUser, PermissionsMixin):
    """
    Custom User model. MUST be linked to an Institution for all non-superusers.
    """
    class UserRole(models.TextChoices):
        SUPERADMIN = 'SUPERADMIN', _('System Super Administrator')
        INSTITUTION_ADMIN = 'INSTITUTION_ADMIN', _('Institution Administrator')
        RESEARCHER = 'RESEARCHER', _('Researcher / Faculty')
        STUDENT = 'STUDENT', _('Student')
        # External collaborators might have a separate role or be handled via a through relationship

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)

    # The crucial link to the Tenant. NULL *only* for the internal superuser.
    institution = models.ForeignKey(
        Institution,
        on_delete=models.CASCADE,
        related_name='members',
        null=True,
        blank=True
    )
    # User's role within their institution. Determines permissions.
    role = models.CharField(max_length=20, choices=UserRole.choices, default=UserRole.RESEARCHER)

    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False) # For Django Admin access
    date_joined = models.DateTimeField(auto_now_add=True)

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name'] # Added required fields for createsuperuser

    class Meta:
        # A user's email must be unique, but we also want to ensure that within an institution,
        # a more natural key (like username) might be unique. This is a placeholder for that concept.
        constraints = [
            # Example: Ensure a user's institutional ID is unique within their institution?
            # models.UniqueConstraint(fields=['institution', 'institutional_id'], name='unique_institutional_id', condition=Q(institution__isnull=False)),
        ]

    def __str__(self):
        return f"{self.email} ({self.institution.name if self.institution else 'System'})"

    def clean(self):
        """Validate model logic before saving."""
        # Superusers must not belong to an institution
        if self.is_superuser and self.institution is not None:
            raise ValidationError({'institution': _('Superusers cannot be associated with an institution.')})
        # Regular users MUST belong to an institution
        if not self.is_superuser and self.institution is None:
            raise ValidationError({'institution': _('Non-superuser accounts must be associated with an institution.')})
        # A SUPERADMIN role likely only makes sense for a few users, perhaps enforce at the application level.

    def save(self, *args, **kwargs):
        # Run full model validation on every save
        self.clean()
        super().save(*args, **kwargs)

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()


class UserManager(BaseUserManager):
    """
    Custom manager for our User model.
    """
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        
        # Enforce that a regular user MUST have an institution
        if not extra_fields.get('is_superuser') and not extra_fields.get('institution'):
            raise ValueError('Regular users must be associated with an institution.')
            
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        # Superusers exist outside the multi-tenant structure
        extra_fields.setdefault('institution', None)

        if extra_fields.get('institution') is not None:
            raise ValueError('Superuser must not have an institution.')

        return self.create_user(email, password, **extra_fields)


# =============================================================================
# 2. RELATIONSHIP & METADATA MODELS
#    These models describe the connections between the foundational models.
# =============================================================================

# A simple through model for many-to-many between User and Institution for admin roles.
# This allows a user to be an admin for multiple institutions (e.g., a uni and its faculty).
class InstitutionAdministrator(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='admin_of_institutions')
    institution = models.ForeignKey(Institution, on_delete=models.CASCADE, related_name='administrators')
    # You could add extra fields like 'scope' (e.g., can manage users, can manage billing)

    class Meta:
        unique_together = ['user', 'institution']

    def __str__(self):
        return f"{self.user.email} administers {self.institution.name}"


# =============================================================================
# 3. CORE OPERATIONAL MODELS
#    These are the main objects that users will interact with daily.
# =============================================================================


class ResearchGroup(models.Model):
    """
    The core operational unit within an institution. All research activities, workspaces,
    and publications are scoped to a Research Group.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # The institution this group belongs to. This is the primary tenancy filter.
    institution = models.ForeignKey(
        Institution,
        on_delete=models.CASCADE, # If an institution is deleted, all its groups are too.
        related_name='research_groups'
    )
    # The leader (Principal Investigator) of the group.
    leader = models.ForeignKey(
        User,
        on_delete=models.PROTECT, # Prevent deletion of a user if they are a leader. Reassign first.
        related_name='led_groups', # More semantically clear than 'owned_groups'
        null=True # Allow for a temporary state without a leader, but should be rare.
    )
    # Members of the research group (excluding the leader). This is a symmetric ManyToMany.
    members = models.ManyToManyField(
        User,
        through='ResearchGroupMembership', # Use an explicit through model for flexibility
        through_fields=('research_group', 'user'),
        related_name='research_groups'
    )

    class Meta:
        # Enforce unique group names within a single institution.
        constraints = [
            models.UniqueConstraint(
                fields=['institution', 'name'],
                name='unique_research_group_name_per_institution'
            )
        ]
        ordering = ['institution__name', 'name']
        indexes = [
            models.Index(fields=['institution', 'is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({self.institution.name})"

    def clean(self):
        """
        Validate model logic before saving.
        """
        # Ensure the group leader belongs to the same institution as the group.
        if self.leader and self.leader.institution != self.institution:
            raise ValidationError(
                {'leader': _('The group leader must be a member of the same institution.')}
            )

    def save(self, *args, **kwargs):
        """
        Run full model validation on every save.
        """
        self.clean()
        super().save(*args, **kwargs)

    @property
    def all_members(self):
        """Return a queryset of all members, including the leader."""
        from django.db.models import Q
        return User.objects.filter(
            Q(led_groups=self) | Q(research_groups=self)
        ).distinct()

    def add_member(self, user, role=ResearchGroupMembership.Role.MEMBER):
        """Helper method to add a member with a specific role."""
        if user == self.leader:
            raise ValidationError(_("The leader is already a member of the group."))
        ResearchGroupMembership.objects.get_or_create(
            research_group=self,
            user=user,
            defaults={'role': role}
        )




class ResearchGroupMembership(models.Model):
    """
    Explicit through model for the ResearchGroup<->User relationship.
    This allows storing additional information about the membership, such as the user's role within the group.
    """
    class Role(models.TextChoices):
        LEADER = 'LEADER', _('Group Leader') # Note: The leader is stored separately on ResearchGroup.
        CO_LEADER = 'CO_LEADER', _('Co-Leader')
        SENIOR_RESEARCHER = 'SENIOR_RESEARCHER', _('Senior Researcher')
        POST_DOC = 'POST_DOC', _('Post-Doctoral Researcher')
        PHD_STUDENT = 'PHD_STUDENT', _('PhD Student')
        PHD_CANDIDATE = 'PHD_CANDIDATE', _('PhD Candidate')
        MSC_STUDENT = 'MSC_STUDENT', _('MSc Student')
        BSC_HONORS_STUDENT = 'BSC_HONORS_STUDENT', _('BSc (Honors) Student')
        BSC_STUDENT = 'BSC_STUDENT', _('BSc Student')
        RESEARCH_ASSISTANT = 'RESEARCH_ASSISTANT', _('Research Assistant')
        RESEARCHER = 'RESEARCHER', _('Researcher')
        STUDENT = 'STUDENT', _('Student')
        MEMBER = 'MEMBER', _('Member')
        COLLABORATOR = 'COLLABORATOR', _('External Collaborator')
        # ... add others as needed ...

    research_group = models.ForeignKey(ResearchGroup, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=Role.choices, default=Role.MSC_STUDENT)
    date_joined = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        # A user can only be a member of a specific group once.
        unique_together = ['research_group', 'user']
        indexes = [
            models.Index(fields=['research_group', 'is_active']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.research_group.name} ({self.role})"

    def clean(self):
        """
        Validate that a user is not added as a member if they are the leader.
        The leader relationship is managed separately.
        """
        if self.research_group.leader == self.user:
            raise ValidationError(
                _("The group leader is managed via the 'leader' field and should not be added as a regular member.")
            )
        # Ensure the user belongs to the same institution as the group.
        if self.user.institution != self.research_group.institution:
            raise ValidationError(
                _("A user must belong to the same institution as the research group to be a member.")
            )



# =============================================================================
# 4. ACTION/EVENT MODELS
#    These models represent temporary states or actions, like invitations.
# =============================================================================

# Add this to your models.py
class Invitation(models.Model):
    """
    Model for handling user invitations to join a specific research group and institution.
    """
    code = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    research_group = models.ForeignKey(ResearchGroup, on_delete=models.CASCADE, related_name='invitations')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_invitations')
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    email = models.EmailField(blank=True, null=True) # Optional: for pre-inviting a specific person
    role = models.CharField(max_length=20, choices=User.UserRole.choices, default=User.UserRole.STUDENT)
    
    # Track usage
    is_used = models.BooleanField(default=False)
    used_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='used_invitations')
    used_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Invite for {self.research_group.name} ({self.code})"

    class Meta:
        indexes = [
            models.Index(fields=['code', 'is_used']), # For fast lookup during registration
        ]



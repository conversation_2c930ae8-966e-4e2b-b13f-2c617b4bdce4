from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status, permissions
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from django.shortcuts import get_object_or_404

from .models import User, Institution, ResearchGroup, Invitation
from .serializers import (
    UserRegistrationSerializer,
    UserSerializer,
    ChangePasswordSerializer,
    InvitationSerializer
)
from .permissions import IsInstitutionAdmin, IsResearchGroupLeader

# This is our test view from before. We can keep it for now.
@api_view(['GET'])
@permission_classes([permissions.AllowAny]) # Explicitly allow unauthenticated access
def hello_api(request):
    data = {'message': 'Hello from the AcademiaSphere API!'}
    return Response(data)


class UserRegistrationView(APIView):
    """
    API view for registering a new user via an invitation code.
    This is the ONLY way to create a regular user account.
    """
    permission_classes = [permissions.AllowAny] # Must be public

    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            # **CRITICAL SECURITY/BUSINESS LOGIC**
            # Validate the invitation code before creating the user
            invitation_code = serializer.validated_data['invitation_code']
            invitation = get_object_or_404(Invitation, code=invitation_code, is_used=False)

            # Check if the invitation has expired
            if invitation.expires_at < timezone.now():
                return Response(
                    {"invitation_code": "This invitation has expired."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Inject the resolved institution from the invitation into the serializer context
            # The serializer's create() method expects this.
            serializer.validated_data['institution'] = invitation.research_group.institution

            # Create the user
            user = serializer.save()

            # Mark the invitation as used
            invitation.is_used = True
            invitation.used_by = user
            invitation.used_at = timezone.now()
            invitation.save()

            # **Optional but recommended: Automatically log the user in**
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)

            response_data = {
                "message": "User registered successfully.",
                "user": UserSerializer(user, context={'request': request}).data,
                "access_token": access_token,
                "refresh_token": str(refresh)
            }

            return Response(response_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            # Log the exception (e.g., using logging.getLogger(__name__).error(...))
            return Response(
                {"error": "An error occurred during registration. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserProfileView(APIView):
    """
    View to retrieve or update the current user's profile.
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user, context={'request': request})
        return Response(serializer.data)

    def patch(self, request):
        serializer = UserSerializer(
            request.user,
            data=request.data,
            partial=True, # Allow partial updates
            context={'request': request}
        )
        if serializer.is_valid():
            # Prevent users from changing their own institution or role via this endpoint
            # These should be admin-managed fields.
            serializer.validated_data.pop('institution', None)
            serializer.validated_data.pop('role', None)

            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    """
    View for changing an authenticated user's password.
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Password updated successfully."})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class InvitationCreateView(APIView):
    """
    View for a Research Group Leader to create a new invitation.
    """
    permission_classes = [permissions.IsAuthenticated, IsResearchGroupLeader]

    def post(self, request):
        # A user must be a group leader to create an invite. This is enforced by the IsResearchGroupLeader permission.
        serializer = InvitationSerializer(data=request.data)

        if serializer.is_valid():
            # Get the group the leader owns (simplified - might need to handle multiple groups)
            research_group = request.user.owned_groups.first()
            if not research_group:
                return Response(
                    {"error": "You must be the leader of a research group to create invitations."},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Create the invitation model instance
            expires_in = serializer.validated_data.get('expires_in', 7)
            invitation = Invitation.objects.create(
                research_group=research_group,
                created_by=request.user,
                email=serializer.validated_data.get('email'),
                role=serializer.validated_data.get('role', User.UserRole.STUDENT),
                expires_at=timezone.now() + timezone.timedelta(days=expires_in)
            )

            # Return the invitation code to the group leader
            return Response({
                "message": "Invitation created successfully.",
                "invitation_code": invitation.code,
                "expires_at": invitation.expires_at
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)




